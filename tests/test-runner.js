const emailService = require('../services/emailService');

async function runTests() {
  console.log('🧪 Email Service Test Suite');
  console.log('=' .repeat(50));
  
  let passed = 0;
  let failed = 0;
  
  // Test 1: Email Service Connection
  console.log('\n📡 Testing email service connection...');
  try {
    const connectionResult = await emailService.testConnection();
    if (connectionResult.success) {
      console.log('✅ Connection test passed');
      console.log(`   Host: ${connectionResult.config.host}`);
      console.log(`   Port: ${connectionResult.config.port}`);
      console.log(`   User: ${connectionResult.config.user}`);
      passed++;
    } else {
      console.log('❌ Connection test failed');
      console.log(`   Error: ${connectionResult.error}`);
      failed++;
    }
  } catch (error) {
    console.log('❌ Connection test failed with exception');
    console.log(`   Error: ${error.message}`);
    failed++;
  }
  
  // Test 2: Template Loading
  console.log('\n📄 Testing template loading...');
  try {
    const templatesResult = await emailService.getAvailableTemplates();
    if (templatesResult.success && templatesResult.templates.length > 0) {
      console.log('✅ Template loading test passed');
      console.log(`   Found ${templatesResult.count} templates:`);
      templatesResult.templates.forEach(template => {
        console.log(`   - ${template}`);
      });
      passed++;
    } else {
      console.log('❌ Template loading test failed');
      console.log(`   Error: ${templatesResult.error || 'No templates found'}`);
      failed++;
    }
  } catch (error) {
    console.log('❌ Template loading test failed with exception');
    console.log(`   Error: ${error.message}`);
    failed++;
  }
  
  // Test 3: Template Compilation
  console.log('\n🔧 Testing template compilation...');
  try {
    const welcomeTemplate = await emailService.loadTemplate('welcome');
    const testData = {
      name: 'Test User',
      email: '<EMAIL>',
      year: new Date().getFullYear()
    };
    const compiledHTML = welcomeTemplate(testData);
    
    if (compiledHTML && compiledHTML.includes('Test User')) {
      console.log('✅ Template compilation test passed');
      console.log('   Welcome template compiled successfully');
      passed++;
    } else {
      console.log('❌ Template compilation test failed');
      console.log('   Compiled template does not contain expected content');
      failed++;
    }
  } catch (error) {
    console.log('❌ Template compilation test failed with exception');
    console.log(`   Error: ${error.message}`);
    failed++;
  }
  
  // Test 4: Environment Variables
  console.log('\n🔐 Testing environment configuration...');
  const requiredEnvVars = ['EMAIL_HOST', 'EMAIL_PORT', 'EMAIL_USER', 'EMAIL_PASS'];
  let envTestPassed = true;
  let missingVars = [];
  
  requiredEnvVars.forEach(varName => {
    if (!process.env[varName]) {
      envTestPassed = false;
      missingVars.push(varName);
    }
  });
  
  if (envTestPassed) {
    console.log('✅ Environment configuration test passed');
    console.log('   All required environment variables are set');
    passed++;
  } else {
    console.log('❌ Environment configuration test failed');
    console.log(`   Missing variables: ${missingVars.join(', ')}`);
    console.log('   Please check your .env file');
    failed++;
  }
  
  // Test 5: Invoice HTML Generation
  console.log('\n📊 Testing invoice HTML generation...');
  try {
    const testInvoiceData = {
      invoiceNumber: 'TEST-001',
      customerName: 'Test Customer',
      items: [
        { description: 'Test Service', quantity: 1, price: 100, total: 100 }
      ],
      total: 100,
      date: new Date().toLocaleDateString(),
      dueDate: 'Upon Receipt'
    };
    
    const invoiceHTML = emailService.generateInvoiceHTML(testInvoiceData);
    
    if (invoiceHTML && invoiceHTML.includes('TEST-001') && invoiceHTML.includes('Test Customer')) {
      console.log('✅ Invoice HTML generation test passed');
      console.log('   Invoice HTML generated successfully');
      passed++;
    } else {
      console.log('❌ Invoice HTML generation test failed');
      console.log('   Generated HTML does not contain expected content');
      failed++;
    }
  } catch (error) {
    console.log('❌ Invoice HTML generation test failed with exception');
    console.log(`   Error: ${error.message}`);
    failed++;
  }
  
  // Summary
  console.log('\n' + '='.repeat(50));
  console.log('📋 Test Summary');
  console.log('='.repeat(50));
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📊 Total:  ${passed + failed}`);
  
  if (failed === 0) {
    console.log('\n🎉 All tests passed! Your email service is ready to use.');
    console.log('\n📝 Next steps:');
    console.log('   1. Configure your .env file with Zoho Mail credentials');
    console.log('   2. Start the server with: npm run dev');
    console.log('   3. Test the API endpoints');
  } else {
    console.log('\n⚠️  Some tests failed. Please review the errors above.');
    console.log('\n🔧 Common fixes:');
    console.log('   1. Check your .env file configuration');
    console.log('   2. Verify Zoho Mail credentials');
    console.log('   3. Ensure all template files exist');
    console.log('   4. Check network connectivity');
  }
  
  console.log('\n🚀 To start the server: npm run dev');
  console.log('📖 API Documentation: http://localhost:3000');
  
  process.exit(failed > 0 ? 1 : 0);
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Run tests
runTests().catch(error => {
  console.error('❌ Test runner failed:', error);
  process.exit(1);
});
