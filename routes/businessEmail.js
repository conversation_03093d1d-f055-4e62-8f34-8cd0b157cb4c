const express = require('express');
const businessEmailController = require('../controllers/businessEmailController');

const router = express.Router();

// Middleware for request logging
router.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - Business Email ${req.method} ${req.path}`);
  next();
});

// Middleware for rate limiting (basic implementation)
const requestCounts = new Map();
const RATE_LIMIT = parseInt(process.env.EMAIL_RATE_LIMIT) || 100;
const RATE_WINDOW = parseInt(process.env.EMAIL_RATE_WINDOW) || 3600000; // 1 hour

const rateLimiter = (req, res, next) => {
  const clientIP = req.ip || req.connection.remoteAddress;
  const now = Date.now();
  
  if (!requestCounts.has(clientIP)) {
    requestCounts.set(clientIP, { count: 1, resetTime: now + RATE_WINDOW });
    return next();
  }
  
  const clientData = requestCounts.get(clientIP);
  
  if (now > clientData.resetTime) {
    clientData.count = 1;
    clientData.resetTime = now + RATE_WINDOW;
    return next();
  }
  
  if (clientData.count >= RATE_LIMIT) {
    return res.status(429).json({
      success: false,
      message: 'Rate limit exceeded. Please try again later.',
      retryAfter: Math.ceil((clientData.resetTime - now) / 1000),
      timestamp: new Date().toISOString()
    });
  }
  
  clientData.count++;
  next();
};

// Apply rate limiting to all routes
router.use(rateLimiter);

// Test business email functionality
// GET /api/business-email/test
router.get('/test', businessEmailController.testBusinessEmail);

// Get available business email templates
// GET /api/business-email/templates
router.get('/templates', businessEmailController.getBusinessTemplates);

// Send business email (main function equivalent to Python sendEmail)
// POST /api/business-email/send
// Body: { 
//   job: {
//     templateType: string,
//     details: {
//       clientName?: string,
//       location?: string,
//       emailTo?: string[],
//       // ... other template-specific fields
//     },
//     vendor?: string,
//     total?: string,
//     service?: string,
//     // ... other fields based on template type
//   }
// }
router.post('/send', businessEmailController.sendBusinessEmail);

// Update email template (for development/testing)
// POST /api/business-email/template/update
// Body: { 
//   templateType: string,
//   subject?: string,
//   body?: string
// }
router.post('/template/update', businessEmailController.updateTemplate);

// Error handling middleware for routes
router.use((error, req, res, next) => {
  console.error('Business email route error:', error);
  res.status(500).json({
    success: false,
    message: 'Internal server error in business email service',
    error: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong',
    timestamp: new Date().toISOString()
  });
});

module.exports = router;
