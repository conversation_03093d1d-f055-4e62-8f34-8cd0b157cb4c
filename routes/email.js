const express = require('express');
const emailController = require('../controllers/emailController');

const router = express.Router();

// Middleware for request logging
router.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// Middleware for rate limiting (basic implementation)
const requestCounts = new Map();
const RATE_LIMIT = parseInt(process.env.EMAIL_RATE_LIMIT) || 100;
const RATE_WINDOW = parseInt(process.env.EMAIL_RATE_WINDOW) || 3600000; // 1 hour

const rateLimiter = (req, res, next) => {
  const clientIP = req.ip || req.connection.remoteAddress;
  const now = Date.now();
  
  if (!requestCounts.has(clientIP)) {
    requestCounts.set(clientIP, { count: 1, resetTime: now + RATE_WINDOW });
    return next();
  }
  
  const clientData = requestCounts.get(clientIP);
  
  if (now > clientData.resetTime) {
    clientData.count = 1;
    clientData.resetTime = now + RATE_WINDOW;
    return next();
  }
  
  if (clientData.count >= RATE_LIMIT) {
    return res.status(429).json({
      success: false,
      message: 'Rate limit exceeded. Please try again later.',
      retryAfter: Math.ceil((clientData.resetTime - now) / 1000),
      timestamp: new Date().toISOString()
    });
  }
  
  clientData.count++;
  next();
};

// Apply rate limiting to all routes
router.use(rateLimiter);

// Test email service connection
router.get('/test', emailController.testConnection);

// Get available email templates
router.get('/templates', emailController.getTemplates);

// Send welcome email
// POST /api/email/welcome
// Body: { email: string, name: string, accountDetails?: object }
router.post('/welcome', emailController.sendWelcomeEmail);

// Send invoice email with HTML attachment
// POST /api/email/invoice
// Body: { 
//   customerEmail: string, 
//   customerName: string, 
//   invoiceData: { 
//     invoiceNumber: string, 
//     items: Array<{description, quantity, price, total}>, 
//     total: number,
//     date?: string,
//     dueDate?: string
//   } 
// }
router.post('/invoice', emailController.sendInvoiceEmail);

// Send notification email
// POST /api/email/notification
// Body: { 
//   email: string, 
//   name?: string, 
//   notification: { 
//     title: string, 
//     message: string, 
//     priority?: 'high'|'normal'|'low',
//     actionUrl?: string,
//     actionText?: string,
//     subject?: string
//   } 
// }
router.post('/notification', emailController.sendNotificationEmail);

// Send custom email with attachments
// POST /api/email/custom
// Body: { 
//   to: string|Array<string>, 
//   subject: string, 
//   content: string|{html: string, text?: string}, 
//   attachments?: Array<{filename, content, contentType}> 
// }
router.post('/custom', emailController.sendCustomEmail);

// Send bulk emails
// POST /api/email/bulk
// Body: { 
//   emails: Array<{
//     to: string,
//     subject: string,
//     template?: string,
//     data?: object,
//     content?: string|object,
//     options?: object
//   }> 
// }
router.post('/bulk', emailController.sendBulkEmails);

// Error handling middleware for routes
router.use((error, req, res, next) => {
  console.error('Email route error:', error);
  res.status(500).json({
    success: false,
    message: 'Internal server error in email service',
    error: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong',
    timestamp: new Date().toISOString()
  });
});

// 404 handler for email routes
router.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Email endpoint not found',
    availableEndpoints: [
      'GET /api/email/test - Test email service connection',
      'GET /api/email/templates - Get available email templates',
      'POST /api/email/welcome - Send welcome email',
      'POST /api/email/invoice - Send invoice email with HTML attachment',
      'POST /api/email/notification - Send notification email',
      'POST /api/email/custom - Send custom email with attachments',
      'POST /api/email/bulk - Send bulk emails'
    ],
    timestamp: new Date().toISOString()
  });
});

module.exports = router;
