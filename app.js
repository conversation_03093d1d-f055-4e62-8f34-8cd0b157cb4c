const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
dotenv.config();

// Import routes
const emailRoutes = require('./routes/email');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Serve static files
app.use(express.static(path.join(__dirname, 'public')));

// Routes
app.use('/api/email', emailRoutes);

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    message: 'Email Service API',
    version: '1.0.0',
    endpoints: {
      'GET /api/email/test': 'Test email service connection',
      'GET /api/email/templates': 'Get available email templates',
      'POST /api/email/welcome': 'Send welcome email',
      'POST /api/email/invoice': 'Send invoice email with HTML attachment',
      'POST /api/email/notification': 'Send notification email',
      'POST /api/email/custom': 'Send custom email with attachments',
      'POST /api/email/bulk': 'Send bulk emails'
    },
    documentation: {
      'Zoho Mail Setup': 'Configure SMTP settings in .env file',
      'Templates': 'HTML templates available in templates/email/ directory',
      'Environment': 'Copy .env.example to .env and configure your settings'
    }
  });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(err.status || 500).json({
    error: {
      message: err.message || 'Internal Server Error',
      status: err.status || 500,
      timestamp: new Date().toISOString()
    }
  });
});

// 404 handler - removed to avoid path-to-regexp issues

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Email Service API running on port ${PORT}`);
  console.log(`📧 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`🌐 API Base URL: http://localhost:${PORT}`);
  console.log(`📋 API Documentation: http://localhost:${PORT}`);
  console.log(`❤️  Health Check: http://localhost:${PORT}/health`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  process.exit(0);
});

module.exports = app;
