{"name": "email-service", "version": "1.0.0", "description": "Professional Email Service API with Zoho Mail integration, featuring template-based emails, bulk sending, and comprehensive email management capabilities", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "node tests/test-runner.js", "test:connection": "node -e \"require('./services/emailService').testConnection().then(r => console.log(JSON.stringify(r, null, 2)))\"", "test:templates": "node -e \"require('./services/emailService').getAvailableTemplates().then(r => console.log(JSON.stringify(r, null, 2)))\"", "lint": "echo '<PERSON><PERSON> not configured yet'", "build": "echo 'Build process not required for this project'", "clean": "rm -rf logs/*.log", "setup": "echo 'Please copy .env file and configure your Zoho Mail credentials'"}, "keywords": ["email", "nodemailer", "zoho-mail", "smtp", "email-templates", "bulk-email", "api", "express", "handlebars"], "author": "Your Name <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/yourusername/email-service.git"}, "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}, "dependencies": {"cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "handlebars": "^4.7.8", "nodemailer": "^7.0.5"}, "devDependencies": {"nodemon": "^3.1.10"}}