<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to Our Platform</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f4f4f4;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 300;
        }
        .content {
            padding: 40px 30px;
        }
        .welcome-message {
            font-size: 18px;
            margin-bottom: 30px;
            color: #555;
        }
        .highlight {
            background-color: #f8f9fa;
            border-left: 4px solid #667eea;
            padding: 20px;
            margin: 30px 0;
        }
        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            margin: 20px 0;
            transition: transform 0.2s;
        }
        .cta-button:hover {
            transform: translateY(-2px);
        }
        .account-details {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 30px 0;
        }
        .account-details h3 {
            margin-top: 0;
            color: #333;
        }
        .detail-item {
            margin: 10px 0;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .detail-label {
            font-weight: bold;
            color: #666;
        }
        .footer {
            background-color: #f8f9fa;
            padding: 30px;
            text-align: center;
            color: #666;
            font-size: 14px;
        }
        .social-links {
            margin: 20px 0;
        }
        .social-links a {
            display: inline-block;
            margin: 0 10px;
            color: #667eea;
            text-decoration: none;
        }
        @media only screen and (max-width: 600px) {
            .container {
                width: 100% !important;
            }
            .header, .content, .footer {
                padding: 20px !important;
            }
            .header h1 {
                font-size: 24px !important;
            }
            .welcome-message {
                font-size: 16px !important;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Welcome to Our Platform!</h1>
            <p>We're excited to have you on board</p>
        </div>
        
        <div class="content">
            <div class="welcome-message">
                <p>Hi <strong>{{name}}</strong>,</p>
                <p>Welcome to our platform! We're thrilled to have you join our community. Your account has been successfully created and you're ready to get started.</p>
            </div>
            
            <div class="highlight">
                <h3>🎉 What's Next?</h3>
                <p>Here are some things you can do to get started:</p>
                <ul>
                    <li>Complete your profile setup</li>
                    <li>Explore our features and tools</li>
                    <li>Connect with other users</li>
                    <li>Check out our getting started guide</li>
                </ul>
            </div>
            
            {{#if accountDetails}}
            <div class="account-details">
                <h3>📋 Your Account Details</h3>
                <div class="detail-item">
                    <span class="detail-label">Email:</span> {{email}}
                </div>
                {{#if accountDetails.username}}
                <div class="detail-item">
                    <span class="detail-label">Username:</span> {{accountDetails.username}}
                </div>
                {{/if}}
                {{#if accountDetails.accountType}}
                <div class="detail-item">
                    <span class="detail-label">Account Type:</span> {{accountDetails.accountType}}
                </div>
                {{/if}}
                {{#if accountDetails.subscriptionPlan}}
                <div class="detail-item">
                    <span class="detail-label">Plan:</span> {{accountDetails.subscriptionPlan}}
                </div>
                {{/if}}
            </div>
            {{/if}}
            
            <div style="text-align: center; margin: 40px 0;">
                <a href="{{loginUrl}}" class="cta-button">Get Started Now</a>
            </div>
            
            <div class="highlight">
                <h3>💡 Need Help?</h3>
                <p>If you have any questions or need assistance, don't hesitate to reach out:</p>
                <ul>
                    <li>📧 Email us at <a href="mailto:{{supportEmail}}">{{supportEmail}}</a></li>
                    <li>📚 Check our documentation and FAQ</li>
                    <li>💬 Join our community forum</li>
                </ul>
            </div>
            
            <p style="margin-top: 40px; color: #666;">
                Thanks again for joining us. We can't wait to see what you'll accomplish!
            </p>
            
            <p style="color: #666;">
                Best regards,<br>
                The Team
            </p>
        </div>
        
        <div class="footer">
            <div class="social-links">
                <a href="#">Facebook</a>
                <a href="#">Twitter</a>
                <a href="#">LinkedIn</a>
                <a href="#">Instagram</a>
            </div>
            <p>&copy; {{year}} Your Company Name. All rights reserved.</p>
            <p>
                <a href="#" style="color: #666;">Unsubscribe</a> | 
                <a href="#" style="color: #666;">Privacy Policy</a> | 
                <a href="#" style="color: #666;">Terms of Service</a>
            </p>
        </div>
    </div>
</body>
</html>
