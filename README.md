# Email Service API

A professional email service API built with Node.js, Express, and Nodemailer, featuring Zoho Mail integration, template-based emails, and comprehensive email management capabilities.

## 🚀 Features

- **Zoho Mail Integration**: Seamless SMTP configuration for Zoho Mail
- **Template-Based Emails**: Professional HTML email templates with Handlebars
- **Bulk Email Support**: Send multiple emails efficiently with rate limiting
- **Multiple Email Types**: Welcome, Invoice, Notification, and Custom emails
- **Responsive Templates**: Mobile-friendly email designs
- **Rate Limiting**: Built-in protection against spam and abuse
- **Error Handling**: Comprehensive error handling and logging
- **Testing Suite**: Built-in tests for all major functionality

## 📋 Quick Start

### 1. Install Dependencies
```bash
npm install
```

### 2. Configure Environment
```bash
# Copy the environment template
cp .env .env.local

# Edit .env with your Zoho Mail credentials
EMAIL_HOST=smtp.zoho.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-password
EMAIL_FROM_NAME=Your Company Name
EMAIL_FROM_ADDRESS=<EMAIL>
```

### 3. Test Configuration
```bash
# Test email service connection
npm run test:connection

# Test template loading
npm run test:templates

# Run full test suite
npm test
```

### 4. Start the Server
```bash
# Development mode (with auto-reload)
npm run dev

# Production mode
npm start
```

## 📡 API Endpoints

### Base URL: `http://localhost:3000/api/email`

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/test` | Test email service connection |
| GET | `/templates` | Get available email templates |
| POST | `/welcome` | Send welcome email |
| POST | `/invoice` | Send invoice email with HTML attachment |
| POST | `/notification` | Send notification email |
| POST | `/custom` | Send custom email with attachments |
| POST | `/bulk` | Send bulk emails |

## 📧 Email Templates

### Welcome Email
```json
POST /api/email/welcome
{
  "email": "<EMAIL>",
  "name": "John Doe",
  "accountDetails": {
    "username": "johndoe",
    "accountType": "Premium"
  }
}
```

### Invoice Email
```json
POST /api/email/invoice
{
  "customerEmail": "<EMAIL>",
  "customerName": "Jane Smith",
  "invoiceData": {
    "invoiceNumber": "INV-001",
    "items": [
      {
        "description": "Web Development Service",
        "quantity": 1,
        "price": 1500,
        "total": 1500
      }
    ],
    "total": 1500,
    "date": "2024-01-15",
    "dueDate": "2024-02-15"
  }
}
```

### Notification Email
```json
POST /api/email/notification
{
  "email": "<EMAIL>",
  "name": "John Doe",
  "notification": {
    "title": "Account Update",
    "message": "Your account has been successfully updated.",
    "priority": "normal",
    "actionUrl": "https://yourapp.com/account",
    "actionText": "View Account"
  }
}
```

## 🔧 Configuration

### Zoho Mail Setup
1. Log into your Zoho Mail account
2. Go to Settings → Mail Accounts
3. Enable "IMAP Access" and "POP Access"
4. Use your email and password in the .env file

### Environment Variables
```bash
# Server Configuration
PORT=3000
NODE_ENV=development

# Zoho Mail SMTP Configuration
EMAIL_HOST=smtp.zoho.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-password

# Email Settings
EMAIL_FROM_NAME=Your Company Name
EMAIL_FROM_ADDRESS=<EMAIL>

# Rate Limiting
EMAIL_RATE_LIMIT=100
EMAIL_RATE_WINDOW=3600000

# Bulk Email Settings
BULK_EMAIL_BATCH_SIZE=50
BULK_EMAIL_DELAY=1000
```

## 🧪 Testing

```bash
# Run all tests
npm test

# Test email connection only
npm run test:connection

# Test template loading only
npm run test:templates
```

## 📁 Project Structure

```
email-service/
├── app.js                     # Main application entry point
├── package.json               # Dependencies and scripts
├── .env                       # Environment configuration
├── .gitignore                 # Git ignore rules
├── README.md                  # Project documentation
├── controllers/
│   └── emailController.js     # Email route handlers
├── services/
│   └── emailService.js        # Email service logic
├── routes/
│   └── email.js              # Email API routes
├── templates/email/
│   ├── welcome.html          # Welcome email template
│   ├── invoice.html          # Invoice email template
│   └── notification.html     # Notification email template
├── tests/
│   └── test-runner.js        # Test suite
├── public/                   # Static files
└── logs/                     # Log files
```

## 🚀 Deployment

### Production Setup
1. Set `NODE_ENV=production`
2. Configure production email credentials
3. Use a process manager like PM2
4. Set up reverse proxy (Nginx)
5. Configure SSL certificates

### Docker Support (Optional)
```dockerfile
FROM node:16-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3000
CMD ["npm", "start"]
```

## 🔒 Security

- Rate limiting on all email endpoints
- Input validation and sanitization
- Environment variable protection
- CORS configuration
- Error message sanitization

## 📚 Support

- 📧 Email: <EMAIL>
- 📖 Documentation: [API Docs](http://localhost:3000)
- 🐛 Issues: [GitHub Issues](https://github.com/yourusername/email-service/issues)

## 📄 License

MIT License - see LICENSE file for details.

---

Made with ❤️ for professional email communication
