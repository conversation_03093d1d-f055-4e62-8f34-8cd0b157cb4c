const emailService = require('../services/emailService');

class EmailController {
  // Test email service connection
  async testConnection(req, res) {
    try {
      const result = await emailService.testConnection();
      
      if (result.success) {
        res.status(200).json({
          success: true,
          message: result.message,
          config: result.config,
          timestamp: new Date().toISOString()
        });
      } else {
        res.status(500).json({
          success: false,
          message: result.message,
          error: result.error,
          timestamp: new Date().toISOString()
        });
      }
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Failed to test email connection',
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  // Get available email templates
  async getTemplates(req, res) {
    try {
      const result = await emailService.getAvailableTemplates();
      res.status(200).json({
        ...result,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Failed to get templates',
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  // Send welcome email
  async sendWelcomeEmail(req, res) {
    try {
      const { email, name, accountDetails } = req.body;

      if (!email || !name) {
        return res.status(400).json({
          success: false,
          message: 'Email and name are required',
          timestamp: new Date().toISOString()
        });
      }

      const templateData = {
        name: name,
        email: email,
        accountDetails: accountDetails || {},
        year: new Date().getFullYear(),
        loginUrl: process.env.LOGIN_URL || '#',
        supportEmail: process.env.SUPPORT_EMAIL || process.env.EMAIL_FROM_ADDRESS
      };

      const result = await emailService.sendTemplateEmail(
        'welcome',
        email,
        `Welcome to our platform, ${name}!`,
        templateData
      );

      if (result.success) {
        res.status(200).json({
          ...result,
          timestamp: new Date().toISOString()
        });
      } else {
        res.status(500).json({
          ...result,
          timestamp: new Date().toISOString()
        });
      }
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Failed to send welcome email',
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  // Send invoice email
  async sendInvoiceEmail(req, res) {
    try {
      const { customerEmail, customerName, invoiceData } = req.body;

      if (!customerEmail || !customerName || !invoiceData) {
        return res.status(400).json({
          success: false,
          message: 'Customer email, name, and invoice data are required',
          timestamp: new Date().toISOString()
        });
      }

      const templateData = {
        customerName: customerName,
        invoiceNumber: invoiceData.invoiceNumber,
        items: invoiceData.items || [],
        total: invoiceData.total || 0,
        date: invoiceData.date || new Date().toLocaleDateString(),
        dueDate: invoiceData.dueDate || 'Upon Receipt',
        companyName: process.env.EMAIL_FROM_NAME || 'Your Company',
        year: new Date().getFullYear()
      };

      // Generate invoice HTML for attachment
      const invoiceHTML = emailService.generateInvoiceHTML(invoiceData);

      const emailOptions = {
        attachments: [
          {
            filename: `invoice-${invoiceData.invoiceNumber}.html`,
            content: invoiceHTML,
            contentType: 'text/html'
          }
        ]
      };

      const result = await emailService.sendTemplateEmail(
        'invoice',
        customerEmail,
        `Invoice ${invoiceData.invoiceNumber} from ${process.env.EMAIL_FROM_NAME || 'Your Company'}`,
        templateData,
        emailOptions
      );

      if (result.success) {
        res.status(200).json({
          ...result,
          timestamp: new Date().toISOString()
        });
      } else {
        res.status(500).json({
          ...result,
          timestamp: new Date().toISOString()
        });
      }
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Failed to send invoice email',
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  // Send notification email
  async sendNotificationEmail(req, res) {
    try {
      const { email, name, notification } = req.body;

      if (!email || !notification) {
        return res.status(400).json({
          success: false,
          message: 'Email and notification data are required',
          timestamp: new Date().toISOString()
        });
      }

      const templateData = {
        name: name || 'User',
        title: notification.title || 'Notification',
        message: notification.message || '',
        priority: notification.priority || 'normal',
        actionUrl: notification.actionUrl || '#',
        actionText: notification.actionText || 'View Details',
        year: new Date().getFullYear(),
        supportEmail: process.env.SUPPORT_EMAIL || process.env.EMAIL_FROM_ADDRESS
      };

      const subject = notification.subject || `${notification.title || 'Notification'}`;

      const result = await emailService.sendTemplateEmail(
        'notification',
        email,
        subject,
        templateData
      );

      if (result.success) {
        res.status(200).json({
          ...result,
          timestamp: new Date().toISOString()
        });
      } else {
        res.status(500).json({
          ...result,
          timestamp: new Date().toISOString()
        });
      }
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Failed to send notification email',
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  // Send custom email
  async sendCustomEmail(req, res) {
    try {
      const { to, subject, content, attachments } = req.body;

      if (!to || !subject || !content) {
        return res.status(400).json({
          success: false,
          message: 'To, subject, and content are required',
          timestamp: new Date().toISOString()
        });
      }

      const emailOptions = {};
      if (attachments && attachments.length > 0) {
        emailOptions.attachments = attachments;
      }

      const result = await emailService.sendCustomEmail(to, subject, content, emailOptions);

      if (result.success) {
        res.status(200).json({
          ...result,
          timestamp: new Date().toISOString()
        });
      } else {
        res.status(500).json({
          ...result,
          timestamp: new Date().toISOString()
        });
      }
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Failed to send custom email',
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  // Send bulk emails
  async sendBulkEmails(req, res) {
    try {
      const { emails } = req.body;

      if (!emails || !Array.isArray(emails) || emails.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'Emails array is required and must not be empty',
          timestamp: new Date().toISOString()
        });
      }

      // Validate each email in the array
      for (let i = 0; i < emails.length; i++) {
        const email = emails[i];
        if (!email.to || !email.subject) {
          return res.status(400).json({
            success: false,
            message: `Email at index ${i} is missing required fields (to, subject)`,
            timestamp: new Date().toISOString()
          });
        }
      }

      const result = await emailService.sendBulkEmails(emails);

      res.status(200).json({
        ...result,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Failed to send bulk emails',
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }
}

module.exports = new EmailController();
