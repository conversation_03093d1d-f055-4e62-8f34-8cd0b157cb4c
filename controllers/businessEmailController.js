const emailService = require('../services/emailService');

// Email templates stored in code (will be populated with your templates)
const EMAIL_TEMPLATES = {
  purchaseRequest: {
    subject: '',
    body: ''
  },
  postGrnStatus: {
    subject: '',
    body: ''
  },
  approved: {
    subject: '',
    body: ''
  },
  rejected: {
    subject: '',
    body: ''
  },
  indentApproval: {
    subject: '',
    body: ''
  },
  piApproval: {
    subject: '',
    body: ''
  },
  purchaseApproval: {
    subject: '',
    body: ''
  },
  purchaseOrder: {
    subject: 'Purchase Order - __CLIENTNAME__ - __LOCATION__ ',
    body: `<html>
<body style="font-size:14px;font-family:arial;background:#F0F2F5;">
    <table width="700" cellpadding="0" cellspacing="0" align="center">
        <tr>
            <td colspan="2" style="padding:50px;">
                <div
                    style="text-align:center;box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);background: #ffffff;  border-radius: 5px;width: 700px;max-width: 100%;margin: 0px auto;padding: 15px 20px;">
                    <h1 style="color: #033A5F;font-size: 28px;margin:0 10px;"> GRN Approval </h1>
                </div>
                <br />
                    <div style="margin-left: 10px;box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);background: #ffffff;  border-radius: 5px;width: 700px;max-width: 100%;margin: 0px auto;padding: 15px 20px;">

                    	<table style="width:100%; border: 1px solid black; border-collapse: collapse; ">

                          <tr>
                            <th align="left" style="padding:5px;"> Purchase Order From </th>
                            <td style="border: 1px solid black; padding:5px;"> __LOCATION__ </td>
                          </tr>
                           <tr>
                            <th style="border: 1px solid black; padding:5px;" align="left">Purchase Order Date </th>
                            <td style="padding:5px;"> __PURCHASEREQUESTDATE__ </td>
                          </tr>
                          <tr>
                            <th style="border: 1px solid black; padding:5px;" align="left"> Purchase Order Id  </th>
                            <td style="border: 1px solid black; padding:5px;"> __PURCHASEREQUESTID__ </td>
                          </tr>
                          <tr>
                            <th style="padding:5px;" align="left"> Role Of Approval</th>
                            <td style="border: 1px solid black; padding:5px;"> __ROLE__ </td>
                          </tr>
                          <tr>
                            <th style="border: 1px solid black; padding:5px;" align="left">Level Of Approval</th>
                            <td style="border: 1px solid black; padding:5px;">__LEVEL__</td>
                          </tr>
                          <tr>
                            <th style="border: 1px solid black; padding:5px;" align="left">Actions</th>
                            <td style="border: 1px solid black; padding:5px;"><button style="color: blue;padding:5px;margin-right: 35px;"><a href=__APPROVE__>Approve</a></button><button style="color: blue;padding:5px;"><a href=__REJECT__>Reject</a></button></td>
                          </tr>

                        </table>
                    <br />
                      <br>
                      <p align="left">
	                    **Please check and verify the document attached below
                    </p>
                    </div>
            </td>
        </tr>
        <tr>
            <td colspan="2">
                <p style="font-size:12px;opacity:.5;text-align:center;">&copy; Copyright © 2022-2023. Digitory Solutions Pvt Ltd. Privacy Policy</p>
            </td>
        </tr>
    </table>
</body>
</html>`
  },
  reportFailed: {
    subject: '',
    body: ''
  },
  systemErrorLog: {
    subject: '',
    body: ''
  },
  report: {
    subject: '',
    body: ''
  }
};

// SMTP Configuration (hardcoded, no DB calls)
const SMTP_CONFIG = {
  smtp: process.env.EMAIL_HOST || 'smtp.zoho.com',
  port: parseInt(process.env.EMAIL_PORT) || 587,
  from: process.env.EMAIL_FROM || process.env.EMAIL_USER,
  password: process.env.EMAIL_PASS
};

class BusinessEmailController {
  
  // Replace all placeholders in template
  replaceAll(repls, str) {
    let result = str;
    for (const [key, value] of Object.entries(repls)) {
      const regex = new RegExp(key.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
      result = result.replace(regex, value);
    }
    return result;
  }

  // Format message based on job data and text type
  formatMessage(msg, job, textType) {
    let repls = {};

    switch (job.templateType) {
      case 'purchaseRequest':
        const restLocationPR = job.details.location.split('@');
        if (textType === 'subject') {
          repls = {
            '__CLIENTNAME__': job.details.clientName,
            '__LOCATION__': restLocationPR[1]
          };
        } else if (textType === 'body') {
          repls = {
            '__PURCHASEREQUESTDATE__': job.details.poDate,
            '__PURCHASEREQUESTID__': job.details.poId,
            '__VENDOR__': job.vendor,
            '__TOTAL__': job.total
          };
        }
        break;

      case 'postGrnStatus':
        const restLocationGRN = job.details.location.split('@');
        if (textType === 'subject') {
          repls = {
            '__CLIENTNAME__': job.details.clientName,
            '__LOCATION__': restLocationGRN[1]
          };
        } else if (textType === 'body') {
          repls = {
            '__GRNFROM__': restLocationGRN[1],
            '__GRNID__': job.details.grnId,
            '__ROLE__': job.details.role,
            '__LEVEL__': job.details.level,
            '__APPROVE__': job.details.approvalLink,
            '__REJECT__': job.details.rejectionLink,
            '__VENDOR__': job.vendor,
            '__TOTAL__': job.total
          };
        }
        break;

      case 'approved':
        const restLocationApproved = job.details.location.split('@');
        if (textType === 'subject') {
          repls = {
            '__SERVICE__': job.service,
            '__CLIENTNAME__': job.details.clientName,
            '__LOCATION__': restLocationApproved[1]
          };
        } else if (textType === 'body') {
          let type;
          switch (job.service) {
            case 'GRN Status': type = 'GRN Id'; break;
            case 'Purchase Approval': type = 'PR Id'; break;
            case 'Purchase Order': type = 'PO Id'; break;
            case 'Indent Approval': type = 'Indent Id'; break;
            case 'PI Approval': type = 'PI Id'; break;
            default: type = 'ID';
          }
          repls = {
            '__SERVICE__': job.service,
            '__FROM__': restLocationApproved[1],
            '__IDTYPE__': type,
            '__ID__': job.details.id,
            '__ROLE__': job.details.role,
            '__LEVEL__': job.details.level,
            '__VENDOR__': job.vendor,
            '__TOTAL__': job.total
          };
        }
        break;

      case 'rejected':
        const restLocationRejected = job.details.location.split('@');
        if (textType === 'subject') {
          repls = {
            '__SERVICE__': job.service,
            '__CLIENTNAME__': job.details.clientName,
            '__LOCATION__': restLocationRejected[1]
          };
        } else if (textType === 'body') {
          let type;
          switch (job.service) {
            case 'GRN Status': type = 'GRN Id'; break;
            case 'Purchase Approval': type = 'PR Id'; break;
            case 'Purchase Order': type = 'PO Id'; break;
            case 'Indent Approval': type = 'Indent Id'; break;
            case 'PI Approval': type = 'PI Id'; break;
            default: type = 'ID';
          }
          repls = {
            '__SERVICE__': job.service,
            '__FROM__': restLocationRejected[1],
            '__IDTYPE__': type,
            '__ID__': job.details.id,
            '__ROLE__': job.details.role,
            '__LEVEL__': job.details.level,
            '__VENDOR__': job.vendor,
            '__TOTAL__': job.total
          };
        }
        break;

      case 'indentApproval':
        const restLocationIndent = job.details.location.split('@');
        if (textType === 'subject') {
          repls = {
            '__CLIENTNAME__': job.details.clientName,
            '__LOCATION__': restLocationIndent[1]
          };
        } else if (textType === 'body') {
          repls = {
            '__INDENTFROM__': restLocationIndent[1],
            '__INDENTID__': job.details.indentId,
            '__ROLE__': job.details.role,
            '__LEVEL__': job.details.level,
            '__APPROVE__': job.details.approvalLink,
            '__REJECT__': job.details.rejectionLink
          };
        }
        break;

      case 'piApproval':
        const restLocationPI = job.details.location.split('@');
        if (textType === 'subject') {
          repls = {
            '__CLIENTNAME__': job.details.clientName,
            '__LOCATION__': restLocationPI[1]
          };
        } else if (textType === 'body') {
          repls = {
            '__FROM__': restLocationPI[1],
            '__PIID__': job.details.piId,
            '__ROLE__': job.details.role,
            '__LEVEL__': job.details.level,
            '__APPROVE__': job.details.approvalLink,
            '__REJECT__': job.details.rejectionLink
          };
        }
        break;

      case 'purchaseApproval':
        const dateString = job.details.prDate;
        const dateObject = new Date(dateString);
        const dateFormatted = dateObject.toLocaleDateString('en-GB'); // DD/MM/YYYY format
        const restLocationPA = job.details.location.split('@');
        
        if (textType === 'subject') {
          repls = {
            '__CLIENTNAME__': job.details.clientName,
            '__LOCATION__': restLocationPA[1]
          };
        } else if (textType === 'body') {
          repls = {
            '__LOCATION__': restLocationPA[1],
            '__PURCHASEREQUESTDATE__': dateFormatted,
            '__PURCHASEREQUESTID__': job.details.prId,
            '__VENDOR__': job.vendor,
            '__TOTAL__': job.total,
            '__ROLE__': job.details.role,
            '__LEVEL__': job.details.level,
            '__APPROVE__': job.details.approvalLink,
            '__REJECT__': job.details.rejectionLink
          };
        }
        break;

      case 'purchaseOrder':
        const restLocationPO = job.details.location.split('@');
        if (textType === 'subject') {
          repls = {
            '__CLIENTNAME__': job.details.clientName,
            '__LOCATION__': restLocationPO[1]
          };
        } else if (textType === 'body') {
          repls = {
            '__LOCATION__': restLocationPO[1],
            '__PURCHASEREQUESTDATE__': job.details.poDate,
            '__PURCHASEREQUESTID__': job.details.poId,
            '__ROLE__': job.details.role,
            '__LEVEL__': job.details.level,
            '__APPROVE__': job.details.approvalLink,
            '__REJECT__': job.details.rejectionLink,
            '__VENDOR__': job.vendor,
            '__TOTAL__': job.total
          };
        }
        break;

      case 'reportFailed':
        if (textType === 'subject') {
          repls = {
            '__CLIENTNAME__': job.tenantId,
            '__LOCATION__': job.details.selectedRestaurants
          };
        } else if (textType === 'body') {
          repls = {
            '__REPORTID__': job.reportNo,
            '__REPORTNAME__': job.details.type,
            '__REQUESTDATE__': job.details.requestDate
          };
        }
        break;

      case 'systemErrorLog':
        if (textType === 'subject') {
          repls = {
            '__CLIENTNAME__': job.client
          };
        } else if (textType === 'body') {
          repls = {
            '__EMAIL__': job.email
          };
        }
        break;

      case 'report':
        const startDate = new Date(job.details.startDate).toLocaleDateString('en-GB');
        const endDate = new Date(job.details.endDate).toLocaleDateString('en-GB');
        const restLocationReport = job.details.selectedRestaurants[0].split('@');
        
        if (textType === 'subject') {
          const sub = `${job.details.type.toUpperCase()} ${restLocationReport[1].toUpperCase()} for ${startDate}`;
          repls = {
            '__SERVICE__': sub
          };
        } else if (textType === 'body') {
          repls = {
            '__REPORT__': job.details.type.toUpperCase(),
            '__NUM__': job.reportNo,
            '__START__': startDate,
            '__END__': endDate
          };
        }
        break;

      default:
        console.warn(`Unknown template type: ${job.templateType}`);
        return msg;
    }

    return this.replaceAll(repls, msg);
  }

  // Main send email function
  async sendBusinessEmail(req, res) {
    try {
      const { job } = req.body;

      if (!job || !job.templateType) {
        return res.status(400).json({
          success: false,
          message: 'Job data with templateType is required',
          timestamp: new Date().toISOString()
        });
      }

      // Get email template
      const emailTemplate = EMAIL_TEMPLATES[job.templateType];
      if (!emailTemplate) {
        return res.status(400).json({
          success: false,
          message: `Email template '${job.templateType}' is not available`,
          availableTemplates: Object.keys(EMAIL_TEMPLATES),
          timestamp: new Date().toISOString()
        });
      }

      // Format subject and body
      const subject = this.formatMessage(emailTemplate.subject, job, 'subject');
      const body = this.formatMessage(emailTemplate.body, job, 'body');

      // Determine recipients
      let recipients;
      if (job.templateType === 'systemErrorLog') {
        recipients = [job.email];
      } else {
        recipients = job.details.emailTo || [];
      }

      if (!recipients || recipients.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'No email recipients specified',
          timestamp: new Date().toISOString()
        });
      }

      // Prepare email options
      const emailOptions = {
        from: {
          name: process.env.EMAIL_FROM_NAME || 'Business System',
          address: SMTP_CONFIG.from
        },
        to: recipients.join(', '),
        subject: subject,
        html: body
      };

      // Add attachment if specified
      if (job.details && job.details.attachment && job.details.filePath) {
        const fs = require('fs');
        const path = require('path');
        
        try {
          const filePath = job.details.filePath;
          const fileName = path.basename(filePath);
          const fileContent = fs.readFileSync(filePath);
          
          emailOptions.attachments = [{
            filename: fileName,
            content: fileContent,
            contentType: 'application/octet-stream'
          }];
        } catch (fileError) {
          console.warn('Failed to attach file:', fileError.message);
          // Continue without attachment
        }
      }

      // Send email using the existing email service
      const result = await emailService.sendCustomEmail(
        recipients,
        subject,
        { html: body },
        emailOptions.attachments ? { attachments: emailOptions.attachments } : {}
      );

      if (result.success) {
        res.status(200).json({
          success: true,
          message: 'Mail sent successfully',
          messageId: result.messageId,
          recipients: recipients,
          templateType: job.templateType,
          timestamp: new Date().toISOString()
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Failed to send email',
          error: result.error,
          templateType: job.templateType,
          timestamp: new Date().toISOString()
        });
      }

    } catch (error) {
      console.error('Business email error:', error);
      res.status(500).json({
        success: false,
        message: 'Something went wrong',
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  // Get available business email templates
  async getBusinessTemplates(req, res) {
    try {
      const templates = Object.keys(EMAIL_TEMPLATES).map(key => ({
        type: key,
        hasSubject: !!EMAIL_TEMPLATES[key].subject,
        hasBody: !!EMAIL_TEMPLATES[key].body
      }));

      res.status(200).json({
        success: true,
        templates: templates,
        count: templates.length,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Failed to get business templates',
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  // Update email template (for development/testing)
  async updateTemplate(req, res) {
    try {
      const { templateType, subject, body } = req.body;

      if (!templateType || !EMAIL_TEMPLATES.hasOwnProperty(templateType)) {
        return res.status(400).json({
          success: false,
          message: 'Invalid template type',
          availableTypes: Object.keys(EMAIL_TEMPLATES),
          timestamp: new Date().toISOString()
        });
      }

      if (subject) EMAIL_TEMPLATES[templateType].subject = subject;
      if (body) EMAIL_TEMPLATES[templateType].body = body;

      res.status(200).json({
        success: true,
        message: `Template '${templateType}' updated successfully`,
        template: EMAIL_TEMPLATES[templateType],
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Failed to update template',
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  // Test business email functionality
  async testBusinessEmail(req, res) {
    try {
      // Test with a sample job
      const testJob = {
        templateType: 'purchaseRequest',
        details: {
          clientName: 'Test Client',
          location: 'test@Test Location',
          poDate: '2024-01-15',
          poId: 'PO-001',
          emailTo: ['<EMAIL>']
        },
        vendor: 'Test Vendor',
        total: '1000.00'
      };

      const template = EMAIL_TEMPLATES[testJob.templateType];
      if (!template.subject || !template.body) {
        return res.status(400).json({
          success: false,
          message: 'Test template not configured. Please add templates first.',
          timestamp: new Date().toISOString()
        });
      }

      const subject = this.formatMessage(template.subject, testJob, 'subject');
      const body = this.formatMessage(template.body, testJob, 'body');

      res.status(200).json({
        success: true,
        message: 'Business email test successful',
        test: {
          templateType: testJob.templateType,
          formattedSubject: subject,
          formattedBody: body,
          recipients: testJob.details.emailTo
        },
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Business email test failed',
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }
}

module.exports = new BusinessEmailController();
