const nodemailer = require('nodemailer');
const handlebars = require('handlebars');
const fs = require('fs').promises;
const path = require('path');

class EmailService {
  constructor() {
    this.transporter = null;
    this.templateCache = new Map();
    this.initializeTransporter();
  }

  // Initialize Nodemailer transporter with Zoho Mail configuration
  initializeTransporter() {
    this.transporter = nodemailer.createTransporter({
      host: process.env.EMAIL_HOST || 'smtp.zoho.com',
      port: parseInt(process.env.EMAIL_PORT) || 587,
      secure: process.env.EMAIL_SECURE === 'true' || false, // true for 465, false for other ports
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS
      },
      tls: {
        rejectUnauthorized: false
      }
    });
  }

  // Test email connection
  async testConnection() {
    try {
      await this.transporter.verify();
      return {
        success: true,
        message: 'Email service connection successful',
        config: {
          host: process.env.EMAIL_HOST,
          port: process.env.EMAIL_PORT,
          user: process.env.EMAIL_USER
        }
      };
    } catch (error) {
      return {
        success: false,
        message: 'Email service connection failed',
        error: error.message
      };
    }
  }

  // Load and compile email template
  async loadTemplate(templateName) {
    try {
      // Check cache first
      if (this.templateCache.has(templateName)) {
        return this.templateCache.get(templateName);
      }

      const templatePath = path.join(__dirname, '..', 'templates', 'email', `${templateName}.html`);
      const templateContent = await fs.readFile(templatePath, 'utf8');
      const compiledTemplate = handlebars.compile(templateContent);

      // Cache the compiled template
      this.templateCache.set(templateName, compiledTemplate);
      
      return compiledTemplate;
    } catch (error) {
      throw new Error(`Failed to load template ${templateName}: ${error.message}`);
    }
  }

  // Get list of available templates
  async getAvailableTemplates() {
    try {
      const templatesDir = path.join(__dirname, '..', 'templates', 'email');
      const files = await fs.readdir(templatesDir);
      const templates = files
        .filter(file => file.endsWith('.html'))
        .map(file => file.replace('.html', ''));
      
      return {
        success: true,
        templates: templates,
        count: templates.length
      };
    } catch (error) {
      return {
        success: false,
        message: 'Failed to get templates',
        error: error.message
      };
    }
  }

  // Send email with template
  async sendTemplateEmail(templateName, to, subject, data, options = {}) {
    try {
      const template = await this.loadTemplate(templateName);
      const html = template(data);

      const mailOptions = {
        from: {
          name: process.env.EMAIL_FROM_NAME || 'Email Service',
          address: process.env.EMAIL_FROM_ADDRESS || process.env.EMAIL_USER
        },
        to: to,
        subject: subject,
        html: html,
        ...options
      };

      const result = await this.transporter.sendMail(mailOptions);
      
      return {
        success: true,
        messageId: result.messageId,
        response: result.response,
        to: to,
        subject: subject,
        template: templateName
      };
    } catch (error) {
      return {
        success: false,
        message: 'Failed to send email',
        error: error.message,
        to: to,
        subject: subject,
        template: templateName
      };
    }
  }

  // Send custom email
  async sendCustomEmail(to, subject, content, options = {}) {
    try {
      const mailOptions = {
        from: {
          name: process.env.EMAIL_FROM_NAME || 'Email Service',
          address: process.env.EMAIL_FROM_ADDRESS || process.env.EMAIL_USER
        },
        to: to,
        subject: subject,
        ...options
      };

      // Add content based on type
      if (typeof content === 'string') {
        if (content.includes('<html>') || content.includes('<div>')) {
          mailOptions.html = content;
        } else {
          mailOptions.text = content;
        }
      } else if (content.html) {
        mailOptions.html = content.html;
        if (content.text) mailOptions.text = content.text;
      }

      const result = await this.transporter.sendMail(mailOptions);
      
      return {
        success: true,
        messageId: result.messageId,
        response: result.response,
        to: to,
        subject: subject
      };
    } catch (error) {
      return {
        success: false,
        message: 'Failed to send custom email',
        error: error.message,
        to: to,
        subject: subject
      };
    }
  }

  // Send bulk emails
  async sendBulkEmails(emails, options = {}) {
    const results = [];
    const batchSize = parseInt(process.env.BULK_EMAIL_BATCH_SIZE) || 50;
    const delay = parseInt(process.env.BULK_EMAIL_DELAY) || 1000;

    for (let i = 0; i < emails.length; i += batchSize) {
      const batch = emails.slice(i, i + batchSize);
      const batchPromises = batch.map(async (email) => {
        try {
          let result;
          if (email.template) {
            result = await this.sendTemplateEmail(
              email.template,
              email.to,
              email.subject,
              email.data || {},
              email.options || {}
            );
          } else {
            result = await this.sendCustomEmail(
              email.to,
              email.subject,
              email.content,
              email.options || {}
            );
          }
          return { ...result, originalEmail: email };
        } catch (error) {
          return {
            success: false,
            error: error.message,
            originalEmail: email
          };
        }
      });

      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);

      // Add delay between batches to avoid rate limiting
      if (i + batchSize < emails.length) {
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    const successful = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;

    return {
      success: true,
      total: emails.length,
      successful: successful,
      failed: failed,
      results: results
    };
  }

  // Generate HTML from invoice data
  generateInvoiceHTML(invoiceData) {
    const { invoiceNumber, customerName, items, total, date, dueDate, companyInfo } = invoiceData;
    
    let itemsHTML = '';
    items.forEach(item => {
      itemsHTML += `
        <tr>
          <td style="padding: 8px; border-bottom: 1px solid #ddd;">${item.description}</td>
          <td style="padding: 8px; border-bottom: 1px solid #ddd; text-align: center;">${item.quantity}</td>
          <td style="padding: 8px; border-bottom: 1px solid #ddd; text-align: right;">$${item.price.toFixed(2)}</td>
          <td style="padding: 8px; border-bottom: 1px solid #ddd; text-align: right;">$${item.total.toFixed(2)}</td>
        </tr>
      `;
    });

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Invoice ${invoiceNumber}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
          .invoice-header { text-align: center; margin-bottom: 30px; }
          .invoice-details { margin-bottom: 20px; }
          .invoice-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
          .invoice-table th { background-color: #f5f5f5; padding: 10px; text-align: left; border-bottom: 2px solid #ddd; }
          .total-row { font-weight: bold; background-color: #f9f9f9; }
        </style>
      </head>
      <body>
        <div class="invoice-header">
          <h1>INVOICE</h1>
          <h2>${invoiceNumber}</h2>
        </div>
        <div class="invoice-details">
          <p><strong>Bill To:</strong> ${customerName}</p>
          <p><strong>Date:</strong> ${date || new Date().toLocaleDateString()}</p>
          <p><strong>Due Date:</strong> ${dueDate || 'Upon Receipt'}</p>
        </div>
        <table class="invoice-table">
          <thead>
            <tr>
              <th>Description</th>
              <th style="text-align: center;">Quantity</th>
              <th style="text-align: right;">Price</th>
              <th style="text-align: right;">Total</th>
            </tr>
          </thead>
          <tbody>
            ${itemsHTML}
            <tr class="total-row">
              <td colspan="3" style="padding: 12px; text-align: right;">TOTAL:</td>
              <td style="padding: 12px; text-align: right;">$${total.toFixed(2)}</td>
            </tr>
          </tbody>
        </table>
        <div style="margin-top: 30px; font-size: 12px; color: #666;">
          <p>Thank you for your business!</p>
          ${companyInfo ? `<p>${companyInfo}</p>` : ''}
        </div>
      </body>
      </html>
    `;
  }
}

module.exports = new EmailService();
